import React, { useEffect, useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  RefreshCw,
  Play,
  CheckCircle,
  Clock,
  Calendar,
  Database,
  Loader2,
  AlertCircle,
} from "lucide-react";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { getSyncHistory, triggerSync } from "@/store/actions/sync.actions";
import { fetchXeroModules } from "@/store/actions/settings.actions";
import useOrganizations from "@/hooks/useOrganizations";
import { toast } from "@/components/ui/sonner";
import { mapReportsEntity } from "@/utils/sync.utils";

const Settings: React.FC = () => {
  const dispatch = useAppDispatch();
  const { entities, isLoading } = useAppSelector((state) => state.settings);
  const { selectedOrganization } = useOrganizations();

  // Local state for sync operations
  const [syncingEntities, setSyncingEntities] = useState<Set<string>>(
    new Set()
  );
  const [syncingAll, setSyncingAll] = useState(false);

  // Fetch Xero modules when component mounts or selected organization changes
  useEffect(() => {
    if (selectedOrganization) {
      dispatch(fetchXeroModules(selectedOrganization) as any);
    }
  }, [dispatch, selectedOrganization]);

  const handleSyncEntity = async (entityName: string) => {
    console.log(`Triggering sync for entity: ${entityName}`);

    if (!selectedOrganization) {
      toast.error("No organization selected", {
        description: "Please select an organization first.",
      });
      return;
    }

    // Handle Reports entity mapping
    let entitiesToSync = [entityName];
    if (entityName === "Reports (P&L, BS, TB)") {
      entitiesToSync = mapReportsEntity([entityName]);
    }

    const payload = {
      companyId: selectedOrganization,
      entities: entitiesToSync,
      priority: "NORMAL" as const,
      fullSync: false,
    };

    try {
      // Set loading state
      setSyncingEntities((prev) => new Set(prev).add(entityName));

      toast.loading(`Syncing ${entityName}...`, {
        id: `sync-${entityName}`,
      });

      console.log("Payload:", payload);
      await dispatch(triggerSync(payload) as any).unwrap();

      toast.success(`${entityName} synced successfully!`, {
        id: `sync-${entityName}`,
        description: `${entitiesToSync.length} ${
          entitiesToSync.length === 1 ? "entity" : "entities"
        } synchronized.`,
      });
    } catch (error) {
      console.error(`Sync failed for ${entityName}:`, error);
      toast.error(`Failed to sync ${entityName}`, {
        id: `sync-${entityName}`,
        description:
          typeof error === "string" ? error : "Please try again later.",
      });
    } finally {
      // Remove loading state
      setSyncingEntities((prev) => {
        const newSet = new Set(prev);
        newSet.delete(entityName);
        return newSet;
      });
    }
  };

  const handleSyncAll = async () => {
    if (!selectedOrganization) {
      toast.error("No organization selected", {
        description: "Please select an organization first.",
      });
      return;
    }

    const enabledEntities = entities.filter((e) => e.enabled);
    if (enabledEntities.length === 0) {
      toast.error("No entities enabled", {
        description: "Please enable at least one entity to sync.",
      });
      return;
    }

    try {
      setSyncingAll(true);

      toast.loading("Starting full sync...", {
        id: "sync-all",
      });

      // Map entities to handle Reports
      const entityNames = enabledEntities.map((e) => e.name);
      const mappedEntities = mapReportsEntity(entityNames);

      const payload = {
        companyId: selectedOrganization,
        entities: mappedEntities,
        priority: "NORMAL" as const,
        fullSync: true,
      };

      console.log("Full sync payload:", payload);
      await dispatch(triggerSync(payload) as any).unwrap();

      toast.success("Full sync completed successfully!", {
        id: "sync-all",
        description: `${mappedEntities.length} entities synchronized.`,
      });
    } catch (error) {
      console.error("Sync all failed:", error);
      toast.error("Full sync failed", {
        id: "sync-all",
        description:
          typeof error === "string" ? error : "Please try again later.",
      });
    } finally {
      setSyncingAll(false);
    }
  };

  return (
    <div className="space-y-8">
      {/* Sync Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="border border-amber-200 shadow-sm bg-gradient-to-br from-white to-amber-50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Total Entities
                </p>
                <p className="text-3xl font-bold text-gray-800">
                  {entities.length}
                </p>
              </div>
              <div className="p-3 bg-amber-100 rounded-full">
                <Database className="h-6 w-6 text-gray-700" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border border-amber-200 shadow-sm bg-gradient-to-br from-emerald-50 to-emerald-100">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-emerald-600">
                  Last Full Sync
                </p>
                <p className="text-lg font-semibold text-emerald-800">
                  2024-06-12
                </p>
                <p className="text-sm text-emerald-700">10:35:00</p>
              </div>
              <div className="p-3 bg-emerald-200 rounded-full">
                <CheckCircle className="h-6 w-6 text-emerald-700" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border border-amber-200 shadow-sm bg-gradient-to-br from-orange-50 to-orange-100">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-orange-600">
                  Next Scheduled
                </p>
                <p className="text-lg font-semibold text-orange-800">Today</p>
                <p className="text-sm text-orange-700">18:00:00</p>
              </div>
              <div className="p-3 bg-orange-200 rounded-full">
                <Calendar className="h-6 w-6 text-orange-700" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Entity Synchronization */}
      <Card className="border border-amber-200 shadow-sm">
        <CardHeader className="pb-4 bg-gradient-to-r from-white to-amber-50 rounded-t-lg">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-xl font-bold text-gray-800">
                Entity Synchronization
              </CardTitle>
              <CardDescription className="text-gray-600 mt-2">
                Monitor and manage data synchronization for all entities.
                Automatic sync runs on schedule, with manual options available.
              </CardDescription>
            </div>
            <Button
              onClick={handleSyncAll}
              className={`
                shadow-md transition-all duration-200 hover:shadow-lg
                ${
                  syncingAll
                    ? "bg-amber-600 hover:bg-amber-700 text-white"
                    : "bg-gray-700 hover:bg-gray-800 text-white"
                }
              `}
              size="lg"
              disabled={isLoading || syncingAll}
            >
              {syncingAll ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <RefreshCw className="h-4 w-4 mr-2" />
              )}
              {syncingAll ? "Syncing All..." : "Sync All Entities"}
            </Button>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <div className="border border-amber-200 rounded-lg overflow-hidden bg-white">
            <Table>
              <TableHeader>
                <TableRow className="bg-amber-50 border-b-2 border-amber-200">
                  <TableHead className="w-[400px] font-semibold text-gray-700 py-4 px-6">
                    Entity
                  </TableHead>
                  <TableHead className="w-[200px] font-semibold text-gray-700 py-4">
                    Last Sync
                  </TableHead>
                  <TableHead className="w-[120px] font-semibold text-gray-700 py-4">
                    Actions
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {entities.map((entity) => {
                  const entityKey =
                    entity.name ===
                    "Reports (P&L, Balance Sheet, Trial Balance)"
                      ? "Reports"
                      : entity.name;
                  const isEntitySyncing = syncingEntities.has(entityKey);

                  return (
                    <TableRow
                      key={entity.id || entity.name}
                      className="hover:bg-amber-50 transition-colors border-b border-amber-100"
                    >
                      <TableCell className="font-medium text-gray-800 py-4 px-6">
                        {entity.name}
                      </TableCell>
                      <TableCell className="font-mono text-sm text-gray-600 py-4">
                        <div className="flex items-center gap-2">
                          <Clock className="h-4 w-4 text-gray-400" />
                          {entity.lastSync}
                        </div>
                      </TableCell>
                      <TableCell className="py-4">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleSyncEntity(entityKey)}
                          disabled={
                            entity.enabled
                              ? isEntitySyncing ||
                                syncingAll ||
                                entity.status === "syncing" ||
                                isLoading
                              : true
                          }
                          className={`
                            transition-all duration-200 border-amber-300 text-gray-700
                            ${
                              isEntitySyncing || entity.status === "syncing"
                                ? "bg-amber-100 border-amber-300 text-amber-700"
                                : "hover:bg-amber-50 hover:border-amber-400"
                            }
                          `}
                        >
                          {isEntitySyncing || entity.status === "syncing" ? (
                            <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                          ) : (
                            <Play className="h-3 w-3 mr-1" />
                          )}
                          {isEntitySyncing || entity.status === "syncing"
                            ? "Syncing..."
                            : "Sync"}
                        </Button>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Settings;

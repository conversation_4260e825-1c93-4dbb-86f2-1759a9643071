// Auth-related async thunks and actions

import { createAsyncThunk } from '@reduxjs/toolkit';
import { LoginCredentials, RegisterCredentials, AuthResponse } from '../types/auth.types';
import { authApi } from '../../api/auth.api';
import { xeroApi } from '../../api/xero.api';
import type { RootState, AppDispatch } from '../index';

// User Authentication Actions

/**
 * User login
 * POST /api/v1/user/login
 */
export const loginUser = createAsyncThunk<
  AuthResponse,
  LoginCredentials,
  {
    state: RootState;
    dispatch: AppDispatch;
    rejectValue: string;
  }
>(
  'auth/loginUser',
  async (credentials, { rejectWithValue }) => {
    try {
      const response = await authApi.login(credentials);
      return response;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : '<PERSON><PERSON> failed');
    }
  }
);

/**
 * User registration
 * POST /api/v1/user/register
 */
export const registerUser = createAsyncThunk<
  AuthResponse,
  RegisterCredentials,
  {
    state: RootState;
    dispatch: AppDispatch;
    rejectValue: string;
  }
>(
  'auth/registerUser',
  async (credentials, { rejectWithValue }) => {
    try {
      const response = await authApi.register(credentials);
      return response;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Registration failed');
    }
  }
);

// Async thunk to logout user
export const logoutUser = createAsyncThunk(
  'auth/logoutUser',
  async (_, { rejectWithValue }) => {
    try {
      const result = await authApi.logout();
      return result;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Logout failed');
    }
  }
);

// Async thunk to fetch current user profile
export const fetchUserProfile = createAsyncThunk(
  'auth/fetchUserProfile',
  async (_, { rejectWithValue }) => {
    try {
      const user: any = await authApi.getCurrentUser();
      return user.data;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch user profile');
    }
  }
);

// Async thunk to check authentication status on app startup
export const checkAuthStatus = createAsyncThunk(
  'auth/checkAuthStatus',
  async (_, { rejectWithValue }) => {
    try {
      const token = localStorage.getItem('auth_token');
      if (!token) {
        throw new Error('No token found');
      }

      // Validate token with backend
      const response = await authApi.getCurrentUser();
      return response;
    } catch (error) {
      // Clear invalid token
      localStorage.removeItem('auth_token');
      return rejectWithValue(error instanceof Error ? error.message : 'Authentication check failed');
    }
  }
);

// Xero Integration Actions

// Async thunk to initiate Xero OAuth flow
export const initiateXeroOAuth = createAsyncThunk(
  'auth/initiateXeroOAuth',
  async (_, { rejectWithValue }) => {
    try {
      const response = await xeroApi.getAuthUrl();
      return response;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to initiate OAuth flow');
    }
  }
);

// Async thunk to complete Xero OAuth flow
export const completeXeroOAuth = createAsyncThunk(
  'auth/completeXeroOAuth',
  async (params: { code: string; state?: string }, { rejectWithValue }) => {
    try {
      const response = await xeroApi.handleCallback({
        code: params.code,
        state: params.state || '',
      });
      return response;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to complete OAuth flow');
    }
  }
);

// Async thunk to refresh auth token
export const refreshAuthToken = createAsyncThunk(
  'auth/refreshAuthToken',
  async (_, { rejectWithValue }) => {
    try {
      const response = await xeroApi.refreshTokens();
      return response;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to refresh auth token');
    }
  }
);

// Async thunk to validate current session
export const validateSession = createAsyncThunk(
  'auth/validateSession',
  async (_, { rejectWithValue }) => {
    try {
      const response = await authApi.validateSession();
      return response;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Session validation failed');
    }
  }
);

// Async thunk to disconnect from Xero
export const disconnectXero = createAsyncThunk(
  'auth/disconnectXero',
  async (_, { rejectWithValue }) => {
    try {
      const response = await authApi.disconnect();
      return response;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to disconnect from Xero');
    }
  }
);

// Async thunk to refresh access token
export const refreshAccessToken = createAsyncThunk(
  'auth/refreshAccessToken',
  async (_, { rejectWithValue }) => {
    try {
      const response = await xeroApi.refreshTokens();
      return response;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to refresh access token');
    }
  }
);

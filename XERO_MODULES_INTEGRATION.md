# Xero Modules API Integration

## Overview

This document outlines the integration of the new Xero modules API endpoint (`/api/v1/xero/modules/:companyId`) into the Entity Synchronization system, replacing the previous mock data with real Xero module data.

## API Endpoint

**Endpoint:** `GET /api/v1/xero/modules/:companyId`

**Response Format:**
```json
{
    "success": true,
    "message": "Available Xero modules retrieved successfully",
    "data": [
        {
            "id": "6fcbc6a6-8dcb-4415-a12c-242e717cd06f",
            "companyId": "116029d3-6a12-4e78-b251-4a36d05ab159",
            "moduleName": "Accounts",
            "lastSyncTime": null,
            "createdAt": "2025-07-08T05:21:26.619Z",
            "updatedAt": "2025-07-08T05:21:26.619Z"
        },
        {
            "id": "4b3d8a88-e33e-4a5c-b8be-6cced6ddf745",
            "companyId": "116029d3-6a12-4e78-b251-4a36d05ab159",
            "moduleName": "Attachments",
            "lastSyncTime": null,
            "createdAt": "2025-07-08T05:21:26.619Z",
            "updatedAt": "2025-07-08T05:21:26.619Z"
        }
    ]
}
```

## Implementation Changes

### 1. API Service (`src/api/settings.api.ts`)

**Added:**
- `XeroModule` interface matching backend response
- `XeroModulesResponse` interface for API response
- `fetchXeroModules()` method to call the new endpoint

```typescript
export interface XeroModule {
  id: string;
  companyId: string;
  moduleName: string;
  lastSyncTime: string | null;
  createdAt: string;
  updatedAt: string;
}

export const settingsApi = {
  fetchXeroModules: async (companyId: string): Promise<XeroModule[]> => {
    const response = await apiClient.get<XeroModulesResponse>(`/xero/modules/${companyId}`);
    return response.data.data;
  },
  // ... other methods
};
```

### 2. Redux Types (`src/store/types.ts`)

**Updated:**
- Enhanced `SyncEntity` interface with additional fields from XeroModule
- Added `XeroModule` interface
- Updated `SettingsState` to include `xeroModules` array

```typescript
export interface SyncEntity {
  id: string;
  name: string;
  lastSync: string;
  status: 'success' | 'pending' | 'syncing' | 'error';
  recordCount?: number;
  enabled: boolean;
  syncFrequency: 'manual' | 'hourly' | 'daily' | 'weekly';
  companyId: string;
  createdAt: string;
  updatedAt: string;
}

export interface SettingsState {
  entities: SyncEntity[];
  xeroModules: XeroModule[];
  // ... other properties
}
```

### 3. Redux Actions (`src/store/actions/settings.actions.ts`)

**Added:**
- `fetchXeroModules` async thunk action

```typescript
export const fetchXeroModules = createAsyncThunk<
  XeroModule[],
  string, // companyId
  {
    state: RootState;
    dispatch: AppDispatch;
    rejectValue: string;
  }
>(
  'settings/fetchXeroModules',
  async (companyId, { rejectWithValue }) => {
    try {
      const modules = await settingsApi.fetchXeroModules(companyId);
      return modules;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch Xero modules');
    }
  }
);
```

### 4. Redux Slice (`src/store/slices/settingsSlice.ts`)

**Changes:**
- Removed mock data
- Added `transformXeroModuleToSyncEntity` function
- Added `xeroModules` to initial state
- Added extraReducers for `fetchXeroModules` action

```typescript
const transformXeroModuleToSyncEntity = (module: XeroModule): SyncEntity => {
  return {
    id: module.id,
    name: module.moduleName,
    lastSync: module.lastSyncTime || 'Never',
    status: module.lastSyncTime ? 'success' : 'pending',
    recordCount: 0,
    enabled: true,
    syncFrequency: 'manual',
    companyId: module.companyId,
    createdAt: module.createdAt,
    updatedAt: module.updatedAt,
  };
};
```

### 5. Settings Component (`src/components/Settings.tsx`)

**Changes:**
- Added import for `fetchXeroModules` action
- Added `useEffect` to fetch modules when component mounts or company changes
- Updated table key to use entity ID

```typescript
// Fetch Xero modules when component mounts or selected organization changes
useEffect(() => {
  if (selectedOrganization) {
    dispatch(fetchXeroModules(selectedOrganization));
  }
}, [dispatch, selectedOrganization]);
```

## Data Flow

1. **Component Mount/Company Selection:**
   - Settings component detects selected organization change
   - Dispatches `fetchXeroModules(companyId)` action

2. **API Call:**
   - Action calls `settingsApi.fetchXeroModules(companyId)`
   - API request sent to `/api/v1/xero/modules/:companyId`
   - Company ID automatically injected by axios interceptor

3. **Data Transformation:**
   - Raw XeroModule data received from API
   - Stored in Redux state as `xeroModules`
   - Transformed to SyncEntity format for UI display
   - Stored in Redux state as `entities`

4. **UI Update:**
   - Settings component re-renders with real module data
   - Table displays actual Xero modules for the selected company
   - Sync operations work with real module names

## Benefits

### 1. **Real Data Integration**
- No more mock data - displays actual Xero modules
- Company-specific module lists
- Real sync timestamps when available

### 2. **Dynamic Module Discovery**
- Modules are fetched based on what's actually available in Xero
- Supports different module configurations per company
- Automatic updates when modules are added/removed

### 3. **Enhanced Sync Accuracy**
- Sync operations target actual available modules
- Better error handling for unavailable modules
- Improved sync status tracking

### 4. **Scalability**
- Easy to add new module types as they become available
- Supports company-specific module configurations
- Extensible for future Xero API enhancements

## Usage Examples

### Fetching Modules
```typescript
// Automatically triggered when company is selected
dispatch(fetchXeroModules(selectedCompanyId));
```

### Accessing Module Data
```typescript
// In component
const { entities, xeroModules, isLoading } = useAppSelector(state => state.settings);

// entities: Transformed for UI display
// xeroModules: Raw API data
// isLoading: Loading state
```

### Sync Operations
```typescript
// Sync specific module
await dispatch(triggerSync({
  companyId: selectedOrganization,
  entities: ['Accounts'], // Uses actual module names from API
  priority: 'NORMAL',
  fullSync: false
}));
```

## Error Handling

- **API Failures:** Graceful error handling with user-friendly messages
- **Missing Company:** Prevents API calls when no company is selected
- **Network Issues:** Retry logic handled by axios interceptor
- **Invalid Responses:** Type validation ensures data integrity

## Future Enhancements

1. **Module Status Indicators:** Visual indicators for module availability
2. **Selective Module Sync:** Allow users to enable/disable specific modules
3. **Module Dependencies:** Handle module interdependencies
4. **Real-time Updates:** WebSocket integration for live module status
5. **Module Configuration:** Per-module sync settings and preferences

## Testing

The integration can be tested by:
1. Selecting different companies in the Settings page
2. Verifying that different module lists appear for different companies
3. Confirming that sync operations work with the fetched modules
4. Testing error scenarios (network failures, invalid company IDs)

## Conclusion

This integration successfully replaces mock data with real Xero module information, providing a more accurate and dynamic entity synchronization experience. The implementation maintains backward compatibility while adding powerful new capabilities for managing company-specific Xero modules.
